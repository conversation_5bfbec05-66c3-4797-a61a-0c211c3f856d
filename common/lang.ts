const header = {
  presents: "| PRESENTS",
  logout: "Log out",
  settings: "Account settings",
  teams: "Teams",
  close: "Close",
  notifications: "Notifications",
  clear: "Clear",
  viewAll: "View all",
  ariaLabels: {
    company: "Your company name",
    openMenu: "Open main menu",
    openNotifications: "Open notifications",
    closeMenu: "Close main menu",
    avatar: "User avatar",
  },
};

const navigation = {
  home: "Home",
  showcases: "Showcases",
  members: "Members",
  support: "Support",
  teamSettings: "Team settings",
  ariaLabels: {
    company: "Your company name",
    openTeamsDropdown: "Open teams dropdown",
    homePageLink: "Link to home page",
  },
};

const dashboard = {
  welcome: "Welcome to Curation Collective",
  description:
    "It’s easy to create your showcases with Curation Connect. Simply hit the create showcase button to get started. Happy story telling!",
  title: "Current Showcases",
  createShowcase: "Create Showcase",
  showcaseHeading: "Showcase",
  step: "Step",
  viewShowcase: "View Showcase",
  viewDraft: "View Draft",
  createDraft: "Create Draft",
  viewLiveShowcase: "View Live",
  listBadges: {
    inProgress: "In progress",
    completed: "Live",
    notCompleted: "Not completed",
    archived: "Archived",
    draft: "Draft",
  },
  badgeStatus: {
    draft: "draft",
    pending: "pending",
    live: "live",
    archived: "archived",
    inProgress: "in-progress",
  },
  showcase: {
    playVideo: "Play Video",
    logo: "Logo",
    title: "Title",
    companyName: "Company Name",
    companyNamePlaceholder: "Enter Company Name",
    companyDescription: "Company Description",
    companyDescriptionPlaceholder:
      "Enter Company Description (maximum 280 characters)",
    showcaseTypeCopy: "Text",
    showcaseTypeCustom: "Custom",
    titlePlaceholder: "Enter Showcase Title",
    publicCompany: "Public Listed Company",
    privateCompany: "Private Company",
    companyType: "Company Type",
    titleError: "Title is required",
    chooseShowcaseType: "Choose Your Showcase Type",
    showcaseType: "Showcase Type",
    chooseShowcaseDesc: "Choose the best way to present your content:",
    textShowcaseType: "text",
    videoShowcaseType: "video",
    videoShowcaseLabel: "Video",
    videoLabel: "Video",
    textShowcaseLabel: "Copy",
    privateShowcaseType: "1",
    privateShowcaseLabel: "Private",
    publicShowcaseType: "0",
    publicShowcaseLabel: "Public",
    videoShowcasePlaceholder: "Enter your video link",
    createShowcaseTitle: "Create New Showcase",
    showcaseConfirmationTitle: "Showcase confirmation",
    showcaseConfirmationDescription:
      "Your {type} showcase has been successfully created.",
    showcaseComingSoonPreview: "Preview coming soon page",
    description: "Description",
    descriptionPlaceholder: "Enter Showcase Description",
    descriptionError: "Description is required",
    descriptionMinLengthError:
      "Description should be at least 10 characters long",
    descriptionMaxLengthError: "Description should be less than 280 characters",
    titleMinLengthError: "Title should be at least 2 characters long",
    titleMaxLengthError: "Title should be less than 100 characters",
    textShowcaseButton: "Text Showcase",
    videoShowcaseButton: "Video Showcase",
    cancelButton: "Cancel",
    manageLinks: "Manage your links",
    heroSection: "Hero Section",
    investmentThesis: "Investment Thesis",
    executiveSummaryTitle: "Executive Summary",
    executiveSummaryDescription:
      "Overview of the investment benefits and key growth catalysts.",
    investmentThesisDescription:
      "Overview of buy and sell case of the business.",
    heroSectionSubtitle:
      "The primary display area visible without scrolling, featuring key messaging and stock information.",
    navBarSectionTitle: "Primary CTA",
    heroSectionTitle: "Header",
    videoSectionTitle: "Video Tab",
    stocksSectionTitle: "Stock Charts Tab",
    buySectionTitle: "Buy Cases",
    sellSectionTitle: "Sell Cases",
    priceTargetLabel: "Price Target",
    priceTargetDisclaimer: "Price Target Disclaimer",
    priceTargetPlaceholder: "Enter price target",
    priceTargetDisclaimerPlaceholder: "Enter price target disclaimer",
    indicatorsBarSectionTitle: "Indicators Bar",
    logoPlaceholder: "No file chosen",
    companyLogo: "Company logo",
    uploadLogo: "Upload logo",
    chooseFile: "Choose a file",
    twitterXlink: "Twitter/X Link",
    primaryCTA: "Primary CTA (Twitter/X Link)",
    headline: "Headline",
    headlinePlaceholder: "Enter headline",
    caption: "Caption",
    primaryCTALabel: "Primary CTA",
    captionPlaceholder: "Enter caption",
    primaryCTAPlaceholder: "CTA URL",
    primaryCTALabelPlaceholder: "Button Label",
    videoCTALabel: "Video CTA",
    videoCTAPlaceholder: "Video URL",
    videoCTALabelPlaceholder: "Button Label",
    stockExchangeLabel: "Stock Exchange",
    stockExchangePlaceholder: "Select Stock Exchange",
    stockTickerLabel: "Stock Ticker",
    searchSymbolLabel: "Select Symbol from Exchange (Recommended)",
    searchSymbolPlaceholder: "Search for Symbol",
    stockTickerPlaceholder: "Enter stock ticker",
    currencyPlaceholder: "Currency",
    raiseAmountLabel: "Raise Amount",
    raiseAmountPlaceholder: "Raise amount",
    equityOfferedLabel: "Equity Offered",
    equityOfferedPlaceholder: "Equity offered",
    valuationLabel: "Valuation (pre-money)",
    valuationPlaceholder: "Valuation",
    revenueLabel: "Revenue",
    revenuePlaceholder: "Revenue",
    marketOpportunityLabel: "Market Opportunity",
    marketOpportunityPlaceholder: "Market opportunity",
    enterTwitterLink: "Enter your Twitter/X link below",
    enterLink: "Enter link",
    showcaseHeading: "Showcase Heading",
    addTheHeading: "Add the heading to the showcase",
    save: "Save",
    next: "Next",
    publish: "Publish",
    back: "Back",
    exit: "Exit",
    createDraft: "Create Draft",
    edit: "Edit Showcase",
    uploadVideo: "Upload Video",
    investmentDeck: "Investment Deck",
    enterUrl: "Enter URL",
    ctaUrl: "CTA URL",
    enterVideoUrl: "Enter your video teaser URL",
    videoTipHeading: "💡 For adding a teaser video:",
    videoTipDescription: `If it's hosted on a platform like YouTube or Vimeo, copy the video URL. Then, within the page editor, paste the video URL into the specified field.`,
    publishing: "💡 Publishing",
    publishingTipDescription:
      "It can take a few minutes for your showcase to appear live on the curation platform but don’t worry thats completely normal!",
    publishShowcase: "Publish the showcase",
    publishShowcaseCaption: "Publish your showcase to Curation Connect",
    showcaseConfiguration: "Showcase Configuration",
    showcaseUrl: "Showcase URL",
    showcasePassword: "Showcase Password",
    confirmPassword: "Confirm Password",
    enablePasswordProtection: "Enable Password Protection",
    toggleShowcaseAlert: "Notify users of the latest research",
    passwordsDontMatch: "Passwords don't match",
    passwordRequired: "Password required",
    videoTipFooter: `Curation Connect Team`,
    uploadJikaLink: "Upload Jika link",
    briefOverview: "Provide a brief overview",
    enterLabel: "Button label",
    addTheCompaniesRelationsLabel:
      "Add the company's investor relations CTA label",
    addTheCompaniesRelationsUrl: "Add the company's investor relations url",
    executionSummary: "Execution Summary",
    executiveSummary: "Executive summary",
    executionDescription:
      "Provide a description of your company and the investment case.",
    usps: `USP's`,
    whyInvest: "Why invest?",
    keyRisks: "Key Risks",
    uspsDescription:
      "What are the three things current shareholders love about your business?",
    usp: "USP",
    ctaButton: "CTA button",
    buttonName: "Button name",
    link: "Link",
    saveChanges: "Save changes",
    enterButtonName: "Enter button name",
    nearTerm: "Near term",
    mediumTerm: "Medium term",
    longTerm: "Long term",
    catalysts: "Catalysts",
    catalystsDescription:
      "What are the key factors that might affect the share price.",
    ceoInterview: "CEO Interview",
    insightsCEO: "Insights from the CEO",
    ceoInterviewLabel: "CEO Interview",
    faqLabel: "Questions and Answers",
    enterFaqDetails: "Enter FAQ Details",
    provideDetailsOfFaq:
      "Commonly asked questions and answers to guide potential investors.",
    question: "Question",
    answer: "Answer",
    companyVideo: "Company video",
    companyContent: "Company Content",
    investorMaterials: "Investor Materials",
    externalInsights: "External Insights",
    insightsFromTheCompany:
      "A curated selection of multimedia content that provides external insights about the company and its industry.",
    investorMaterialsCaption:
      "A curated repository of essential resources, providing detailed insights into the company.",
    videoLink: "Video link",
    podcastLink: "Podcast link",
    aritcleLink: "Article link",
    enterYourQuestion: "Enter your question",
    keyInfluencers: "Key Influencers",
    keyInfluencersDescription: "Manage important social media profiles",
    name: "Name",
    enterName: "Enter name",
    socialLink: "Social link",
    enterSocialMediaLink: "Enter social media link",
    uploadCompanyResearch: "Upload Company research",
    research: "Research",
    researchTitle: "Research Title",
    enterTitle: "Enter title",
    manageYourArticles: "Manage your articles",
    addEditDeleteArticles:
      "A selection of articles offering detailed insights into the company's industry position and performance.",
    articleLink: "Article link",
    pasteLinkHere: "Paste link here",
    teamMemberInformation: "Team member information",
    teamMemberInformationCaption:
      "Profiles of key team members, highlighting their expertise and roles within the company.",
    role: "Role",
    enterRole: "Enter role",
    enterDescriptionMax: "Enter description",
    linkedInLink: "LinkedIn link",
    enterLinkedInLink: "Enter LinkedIn profile link",
    xLink: "X Link",
    enterXLink: "Enter X link",
    contactForm: "Contact form",
    pleaseFillOutTheForm: "Please fill out the form below",
    emailAddress: "Email address",
    enterYourEmail: "Enter your email",
    imageUpload: "Image upload",
    chooseImageFile: "Choose image file",
    disclaimer: "Disclaimer",
    disclaimerDescription:
      "Create a bespoke disclaimer for each showcase. If you'd like to use the default disclaimer, leave this field blank.",
    disclaimerLabel: "Showcase Disclaimer",
    curationAI: "Curation AI",
    discoveryPage: "Discovery Page",
    discoveryPageDesc: "Settings for Discovery page",
    curationAIDescription:
      "Enable the CurationAI for this businesses showcase and upload its knowledge base.",
    aiConfiguration: "AI Configuration",
    enableCurationAI: "Enable Curation AI",
    useDocsBelow: "Enable Curation AI to use it’s own knowledge",
    deleteFile: "Delete file",
    prompts: "Prompts",
    promptsSubHeader: "Predefine questions to help users interact with Curation quickly.",
    cancelDeleteFilePopup: "Cancel",
    confirmDeleteFilePopup: "Confirm",
    deleteConfirmation: "Are you sure you want to delete this file?",
    knowledgeBase: "Knowledge Base",
    addNewFile: "+ Add new file",
    addNewQuestion: "+ Add new question",
    removePrompt: "Remove",
    publishDraft: "Publish draft",
    preview: "Preview",
    previewTheShowcase: "Preview the showcase",
    publishYourDraft: "Publish your draft of the showcase:",
    publishTheShowcase: "Publish the showcase",
    publishYourShowcase: "Publish your showcase to curation connect:",
    createHubspotList: "Create HubSpot list",
    submit: "Submit",
    approval: "Approval",
    htmlCode: "Html code",
    embed: "Embed <>",
    randomUrl: "www.randomurl.com",
    viewAllShowcases: "View all showcases",
    video: "Video",
    podcasts: "Podcasts",
    pdfUppercase: "PDF",
    article: "Article",
    collectionItems: "Collection items",
    collectionTheme: "Collection theme",
    enterCollectionTheme: "Enter collection theme",
    videoPreview: "Video preview",
    allShowcases: "All showcases",
    steps: "Steps",
    invalidLink: "Invalid link. Please enter another link.",
    metaNotFound:
      "Meta data not found for this link. Title and/or description of link will remain blank.",
    completeAllSteps:
      "Please complete all required sections before previewing or publishing your showcase",
    showcaseCreation: "SHOWCASE CREATION",
    experts: "Experts",
    expertsDescription: "Insights from the experts",
    expert: {
      expertName: "Expert name",
      expertNamePlaceholder: "Enter expert name",
      expertField: "Expert field",
      expertFieldPlaceholder: "Enter information",
      expertPhoto: "Upload photo",
      expertPhotoPlaceholder: "No file chosen",
      expertAudiance: "Audience size",
      expertAudiancePlaceholder: "Enter audience",
      expertSocialLinks1: "Twitter link",
      expertSocialLinks2: "Linkedin link",
      expertSocialLinks3: "Website link",
      socialLinkPlaceholder: "Enter URL (optional)",
      socialLinkOptional: "Enter linkedin url",
      platfromType: "Content platform type",
      cardType: "Card type",
      content: "Content",
      CTAlink: "CTA link",
      platformPlaceholder: "Select platform type",
      typePlaceholder: "Select card type",
      contentPlaceholder: "Text, Link",
      CTAlinkPlaceholder: "URL",
      CTAlinkLabel: "Label",
    },
    imageHelperText: "PNG, JPG or GIF (max 1MB).",
    logoImageHelperText: "svg, png, or jpg (max. 1mb)",
    header: {
      login: "Login",
      signup: "SignUp",
      logout: "Logout",
      loginSignup: "Login/SignUp",
    },
  },
  restricted: {
    restrictedAccess: "Restricted Access",
    caption: "Enter Showcase Password",
    submit: "Submit",
    placeholder: "Enter password",
  },
  showcaseDelete: {
    deleteConfirmation: "Delete Confirmation",
    confirmation:
      "Are you sure you want to delete this showcase? This cannot be undone.",
    cancelLabel: "Cancel",
    deleteLabel: "Delete showcase",
    showcaseDeleted: "Showcase deleted successfully",
    showcaseDeleteError: "Error deleting showcase",
    currentShowcase: "Current Showcases",
    noResultsFound: "No results found",
  },
  serverErrors: {
    invalidToken: "Invalid showcase token.",
  },
  inviteInvestorSignup: "Copy invite Investor url",
};

const buttonLabels = {
  remove: "Remove",
  addnewCollection: "+ Add new collection",
  addContentCard: "+ Add new content card",
  addExpertCard: "+ Add new expert",
  removeExperts: "Remove Expert",
  removeCard: "Remove Card",
  chooseFile: "Choose file",
};

const home = {
  title: " Platform Template",
  visitDocsText: "Visit Documentation",
  signUp: "Sign up",
  login: "Log in",
  logout: "Log out",
  payment: "Payment",
  loggedIn: "You are signed in successfully!",
  admin: "Admin",
  dashboard: "Dashboard",
  ariaLabels: {
    homePageLink: "Link to home page",
    visitDocs: "Visit documentation",
  },
};

const signUp = {
  header: "Create an account",
  signUpCompleted: "Welcome to the community",
  headerRegister1: "Join our Movement",
  headerRegister: "Discover New Equity Stories",
  headerRegisterWatchList: "Add to Watchlist",
  registerDescription:
    "Sign up with your Google or Yahoo account or enter your email below.",
  registerDescription1:
    "Welcome to Shareflix — a platform built for retail investors.<br/> We're surfacing untold equity stories from around the world, bringing you the tools, insights, and community to make informed investment decisions.",
  registerDescriptionMobile:
    "Explore new equity stories through unrestricted access to our comprehensive research suite, backed by expert validation and insights.",
  registerDescriptionMobile1:
    "Welcome to Shareflix — a platform built for retail investors. We're surfacing untold equity stories from around the world bringing you the tools, insights, and community to make informed investment decisions.",
  registerDescWithShowcase:
    "Access exclusive investment research, curated insights from a network of investors ($250B AUM represented), live CEO webinars and curated summary of {company} news.",
  firstName: "First name",
  lastName: "Last name",
  email: "Email",
  newEmail: "New email",
  confirmationEmail: "Re-enter email",
  emailPlaceholder: "Email address",
  password: "Password",
  passwordPlaceholder: "Password",
  confirmPassword: "Re-enter password",
  submitButtonLabel: "Continue",
  iAgree: "I agree to the",
  terms: "Terms & Conditions",
  acknowledge: "and acknowledge the",
  privacyPolicy: "Privacy Policy",
  accountExists: "Already have an account?",
  login: "Login",
  errorLogingUp: "Error Loging up.",
  errorSigningUp: "Error signing up.",
  errorVerifyingOtp: "Error verifying OTP.",
  errorResendingOtp: "Error resending OTP email.",
  otpSentSuccess: "Verification code has been sent. Please check your email.",
  save: "Save",
  errorResendingVerificationEmail: "Error resending verification email.",
  verificationEmailResent: "Verification email resent.",
  termsDesc1: "By signing up, you agree to Curation Connect’s",
  termsDesc2: "of Service and",
  termsLabel: "Terms",
  privacyPolicyLabel: "Privacy Policy",
  info: {
    heading: "Create your FREE account",
    companyExistInfo:
      "Get curated summary of the key company news, sector updates and why they matter. Keeping you effortlessly informed.",
    info1TitlePart1: "Unrestricted access to our full research suite",
    info1TitlePart2: "$250B AUM ",
    info1TitlePart3: "network of investors.",
    info2TitlePart1: "Discover new equity stories",
    info2TitlePart2: "Exclusive ",
    info2TitlePart3: "Research & Investor Briefings",
    info3TitlePart1: "Get the pro’s validation and insight",
    info3TitlePart2: "CEO ",
    info3TitlePart3: "webinars",
    disclaimer: "Setting up your account takes ~50s",
    betaAccess: "Beta testers get early access.",
    alwaysFree: "Always free. No fees. No fine print.",
    globalStories: "Global stories. Jargon free. Collective intelligence.",
    shortInfo1Part1: "Discover ",
    shortInfo1Part2: "exclusive ",
    shortInfo1Part3: "research & investor briefings",
    shortInfo2Part1: "Get curated insights from a ",
    shortInfo2Part2: "$250B AUM ",
    shortInfo2Part3: "network of investors",
    shortInfo3Part1: "Access live CEO webinars",
    shortInfo4Part1: "Receive a ",
    shortInfo4Part2: "curated ",
    shortInfo4Part3: "summary of company news.",
  },
  infoType: {
    short: "short",
    full: "full",
  },
};

const signIn = {
  header: "Sign in to your account",
  email: "Email",
  emailPlaceholder: "Email address",
  password: "Password",
  passwordPlaceholder: "Password",
  submitButtonLabel: "Sign in",
  forgotPassword: "Forgot password?",
  notAMember: "Not a member?",
  createAccount: "Create an account",
  dontHaveAccount: "Don’t have an account?",
  signUp: "Sign up",
  signInError: "Please enter a valid email and password.",
  rememberMe: "Remember me",
  login: "Login",
  loginDesc:
    "Login with your Google  or Yahoo account or enter your email and password below.",
  notMember: "Not a member?",
  pageViewTitle: "Login | Investor",
};

const verifyOtp = {
  header: "We have sent a code to your email",
  subheading:
    "Please verify your account by entering the 6-digit verification code just sent to ",
  didNotReceive: "Didn't receive an email?",
  contactSupport: "Contact support",
  continue: "Continue",
  resend: "Resend",
};

const forgotPassword = {
  resetPassword: "Reset Password",
  instructions:
    "Please enter your email and we’ll send you instructions on how to reset your password",
  emailAddress: "Email address",
  sendEmail: "Send email",
  noEmail: "Didn’t receive an email?",
  contactSupport: "Contact support",
  errorResettingPassword: "Error resetting password.",
  tooManyAttempts: "Too many reset password attempts. Please try again later",
  success: "Email sent!",
  back: "Back",
};

const resetPassword = {
  header: "Reset password",
  headerSuccess: "Password reset",
  instruction: "Please enter your new password",
  newPassword: "Password",
  confirmPassword: "Re-enter password",
  resetPasswordCTA: "Reset Password",
  success: "Password reset",
  successMessage:
    "Your password has been reset. Please sign in to your account",
  error: "Error resetting password.",
  login: "Sign in",
  appSuccess: "Reset password!",
};

const passwordCreate = {
  header: "Create a password",
  newPassword: "Password",
  confirmPassword: "Re-enter password",
  passwordCreateCTA: "Create account",
  error: "Error creating password.",
  login: "Sign in",
  headerSuccess: "Password set",
  successMessage: "Your password has been set. Please sign in to your account",
};

const auth = {
  emailFormat: "Please enter a valid email address",
  emailsMustMatch: "Email fields must be the same",
  passwordFormat: "Please enter a valid password",
  passwordLengthError: "Password must be at least 8 characters long",
  passwordMustContainUppercaseLetter:
    "Password must contain at least one uppercase letter",
  passwordMustContainerLowercaseLetter:
    "Password must contain at least one lowercase letter",
  passwordMustContainSpecialCharacter:
    "Password must contain at least one special character",
  passwordMustContainNumber: "Password must contain at least one number",
  passwordMustNotContainConsecutiveCharacters:
    "The password must not contain more than 2 consecutive identical characters",
  passwordMustNotContainConsecutiveCharactersByASCII:
    "The password must not contain more than 4 consecutive alphanumeric characters",
  fieldRequired: "This field is required",
  passwordValidationCheck: "Please make sure your passwords match",
  password: {
    lowercase: "One lowercase character",
    uppercase: "One uppercase character",
    number: "One number",
    special: "One special character",
    length: "8 characters minimum",
    consecutiveCharacters: "No more than 2 consecutive identical characters",
    consecutiveCharactersByASCII:
      "No more than 4 consecutive alphanumeric characters",
  },
  consent: "You must agree to Privacy Policy to proceed",
};

const signUpSuccess = {
  header: "Thanks for signing up",
  login: "Sign in",
  description:
    "Please verify your account by clicking on the activation link you received in the email",
  emailNotReceived: "Didn’t receive the email?",
  resendEmail: "Resend email",
};

const verifyAccount = {
  header: "Check your inbox",
  login: "Sign in",
  description:
    "Please verify your account by clicking on the activation link you received in the email.",
  emailNotReceived: "Didn’t receive the email?",
  resendEmail: "Resend email",
  logoutButton: "Log out",
  resendInviteSuccess: "Invite has been resent",
  resendInviteError: "Error resending invite. Please try again later",
  verifyError: "Session expired. Please try again.",
};

const confirmEmailSuccess = {
  header: "Congrats {userName}!",
  description: "You’re now officially our youngest member!",
  viewCMS: "View CMS",
};

const signUpVerification = {
  header: "Thanks for signing up",
  instructions:
    "Please verify your email by entering the confirmation code sent to %email%",
  code: "Verification code",
  verifyAccount: "Verify account",
  noCode: "Didn’t receive a code?",
  resendCode: "Resend code",
};

const settingsNavigation = {
  general: "General",
  notifications: "Notifications",
  plan: "Plan",
  billing: "Billing",
  support: "Support",
  account: "Account",
};

const settings = {
  header: "Account settings",
  billing: {
    header: "Payment",
    description: "Manage your payment details",
    paymentMethod: "Payment method",
    ending: "Ending in",
    expires: "Expires",
    lastUpdated: "Last updated on",
    noPaymentMethod: "No payment method added",
    edit: "Edit",
    undefinedPaymentDetails: "N/A",
  },
  general: {
    userInfoHeader: "User information",
    userInfoDescription: "Your personal information tied to your account",
    userInfoUpdateSuccess: "User information updated successfully",
    userImageUpdateSuccess: "User image updated successfully",
    userImageProcessed: "User image processed successfully",
    userImageProcessingFailed: "User image processing failed",
    userImageProcessingTimeout:
      "User image processing timeout, please try again later",
    userImageUpdateError: "Error updating user image",
    errorWhileReadingFile: "Error while reading file",
    userImageCroppingError: "An error occurred while saving the image",
    userImageUploadProcessError: "Error checking the user image upload process",
    imageSizeError: "Image size should be less than",
    pdfSizeError: "File size should be less than",
    imageTypeError: "Image type should be JPG or PNG",
    pdfTypeError: "File type should be PDF",
    userInfoUpdateError: "Error updating user information",
    emailChangeInfo: `*Changing the email address will result in logging out and the need to confirm the new e-mail
    address by clicking on the activation link sent to your inbox.`,
    profileImage: "Profile image",
    profileImageDescription: "Your profile image",
    update: "Update",
    processing: "Processing...",
    userAvatar: "User avatar",
    deleteImage: "Delete",
    userImageDeleteSuccess: "User image deleted successfully",
    userImageDeleteError: "Error deleting user image",
    errorWhileFetchingUserDetails: "Error while fetching user details",
    imageUploadStatusInvalid: "Invalid status",
  },
};

const dropdown = {
  ariaLabels: {
    dropdownButton: "Dropdown button",
  },
};

const modal = {
  ariaLabels: {
    close: "Close modal",
  },
};

const flyoutMenu = {
  ariaLabels: {
    open: "Flyout menu button",
  },
};

const toggleButton = {
  ariaLabels: {
    toggle: "Toggle button",
  },
};

const cropperModal = {
  save: "Save",
  cancel: "Cancel",
  crop: "Crop image",
  zoom: "Zoom",
};

const confirmationModal = {
  confirm: "Confirm",
  cancel: "Cancel",
};

const removeProfileImageModal = {
  title: "Remove profile image",
  description: "Are you sure you want to remove your profile image?",
};

const inviteMemberModal = {
  title: "Invite member",
  sendInvite: "Send invite",
  cancel: "Cancel",
  email: "Email",
  emailPlaceholder: "Email",
  role: "Role",
  selectUserRole: "Select user role",
  fieldRequired: "This field is required",
  emailFieldRequired: "Please enter an email address",
  roleFieldRequired: "Please select a role",
  emailFormat: "Please enter a valid email address",
  admin: "Admin",
  member: "Member",
  adminRadioDescription:
    "Has full administrative access to the entire organisation.",
  memberRadioDescription: "Can see other members.",
  inviteMemberError: "Error inviting member. Please try again later",
  inviteMemberSuccess: "Invite has been sent",
};

const notifications = {
  allClear: "All clear",
  header: "Notifications",
  description: "Alerts from your team members",
  errorMarkingNotificationAsRead: "Error marking notification as read",
  notificationMarkedAsRead: "Notification marked as read",
  allNotificationsMarkedAsRead: "All notifications marked as read",
  errorMarkingAllNotificationsAsRead: "Error marking all notifications as read",
  title: "Title",
  message: "Message",
  markAsRead: "Mark as read",
  emptyNotifications: "You don’t have any notifications yet",
  uploadPicture: "Upload picture",
};

const payments = {
  header: "Payment",
  paymentCheckoutError: "Error checking out payment session",
};

const team = {
  search: "Search by email",
  members: "Members",
  noResults: "No users found",
  noMatchingResults: "No members match your search, please try again",
  noInvitedUsersMatchingResults:
    "No invited users match your search, please try again",
  noInvitedUsersResults: "No invited users found",
  inviteMember: "Invite member",
  editUserDetails: "Edit user details",
  deleteAccount: "Delete account",
  deleteInvitation: "Delete invitation",
  resendInvite: "Resend invite",
  userNotFound: "User not found",
  activeUsersTab: "Active",
  pendingUsersTab: "Pending",
  pendingInvitation: "Pending invitation",
  inviteResent: "Invite has been resent",
  inviteMemberError: "Error inviting member. Please try again later",
  deleteInvitedMemberError:
    "Error deleting invited member. Please try again later",
  deleteInvitedMemberSuccess: "Invited member has been deleted",
  deleteUserError: "Error deleting user. Please try again later",
  deleteUserSuccess: "User has been deleted",
  resendInviteError: "Error resending invite. Please try again later",
  resendInviteSuccess: "Invite has been resent",
  unverifiedMember: "Unverified member",
  teamMembers: "Team members",
  ariaLabels: {
    table: "Team members table",
    invitedMembersTable: "Invited members table",
  },
};

const teamSettings = {
  header: "Team settings",
  teamName: "Team name",
  teamNameDescription: "This is your team's visible name",
  update: "Update",
  leaveTeam: "Leave team",
  deleteTeam: "Delete team",
};

const teams = {
  header: "Teams",
  search: "Search",
  tableColumns: {
    name: "Name",
    role: "Role",
    members: "Members",
  },
  noResults: "No teams found",
  settings: "Settings",
  manageMembers: "Manage members",
  createNewTeam: "Create new team",
  ariaLabels: {
    table: "Teams table",
  },
};

const responseErrorMessages = {
  unauthorized: "Unauthorized",
  specifyFeatureFlag: "Please specify a flag to check",
  flagNotFound: "Flag not found",
  fetchError: "Fetch error",
  serverError: "Server error",
  methodNotAllowed: "Method not allowed",
};

const signupVerificationExpired = {
  header: "This link has already been used or expired",
  description: "We can send a new link to help you get started",
  resendLink: "Resend link",
};

const resetPasswordExpired = {
  header: "This link has already been used or expired",
  description: "We can send a new link to help you reset your password",
  resendLink: "Resend link",
};

const support = {
  header: "Support",
  description: "Contact our support team",
};

const boundaryError = {
  title: "Oops something's not right!",
  description:
    "We seem to be having some problems loading our platform, please check back soon or contact ",
  buttonText: "Try again",
};

const deleteUserModal = {
  title: "Delete user account",
  description: "Are you sure you want to delete this user account?",
  delete: "Delete",
  deleteUserSuccess: "The user account has been deleted",
  deleteUserError: "Error deleting user. Please try again later",
};

const deleteInvitedUserModal = {
  title: "Delete user invitation",
  description: "Are you sure you want to delete this user invitation?",
  delete: "Delete",
  deleteUserSuccess: "The user invitation has been deleted",
  deleteUserError: "Error deleting invitation. Please try again later",
};

const editUserModal = {
  title: "Edit user details",
  firstName: "First name",
  lastName: "Last name",
  role: "Role",
  cancel: "Cancel",
  saveChanges: "Save changes",
  savingChangesSuccess: "Changes successfully saved",
  savingChangesError: "Changes were not saved. Please try again later",
  roleFieldRequired: "Please select a role",
  fieldRequired: "This field is required",
  selectUserRole: "Select user role",
};

const leaveTeamModal = {
  title: "Leave team?",
  description:
    "You are about to leave this team. In order to regain access at a later time, a Team Admin must invite you.",
  secondLineDescription: "Are you sure you want to continue?",
  leave: "Leave",
  leaveTeamSuccess: "You have successfully left the team.",
  leaveTeamError: "Error leaving the team. Please try again later",
};

const leaveTeamDeniedModal = {
  description:
    "You cannot leave since you are the only remaining admin of the team.",
  close: "Close",
};

const deleteTeamModal = {
  title: "Delete team?",
  description:
    "Are you sure you want to delete this team? This action is not reversible.",
  delete: "Delete",
  deleteTeamSuccess: "Your team has been successfully deleted.",
  deleteTeamError: "Error deleting team. Please try again later",
};

const createTeamModal = {
  fieldRequired: "This field is required",
  title: "Create your team",
  nameYourTeam: "Name your team",
  createTeam: "Create team",
  cancel: "Cancel",
};

const onboarding = {
  header: "Let's complete your profile",
  signOut: "Sign out",
  backToAdminPanel: "Back to Admin Panel",
  loggedInAs: "Logged in As: ",
  continue: "Continue",
  step: "Step",
  of: "of",
  firstName: "First name",
  lastName: "Last name",
  dateOfBirth: "Date of birth",
  ariaTitleUsername: "Enter User Name",
  ariaTitleBirthDate: "Enter Birth Date",
  onboardingCompleted: "Onboarding completed",
  user: {
    header: "Join Curation Connect for free",
    registerDescription:
      "Access live CEO webinars, exclusive investment research, and curated insights from a $250B network of investors.",
    registerDescWithShowcase:
      "Access exclusive investment research, curated insights from a network of investors ($250B AUM represented), live CEO webinars and curated summary of {company} news.",
    infoHeading:
      "Join a community of over 7000 investors researching monthly with Curation Connect.",
    showcaseOneRaised: "$475m MC - NASDAQ",
    showcaseOneTitle: "MeiraGTx - Biotech",
    showcaseTwoRaised: "£500m MC - FTSE250",
    showcaseTwoTitle: "Renewi - Recycling",
    showcaseThreeRaised: "$300m MC - NASDAQ",
    showcaseThreeTitle: "TMC - Mining",
    showcaseFourRaised: ">£200m MC - FTSE250",
    showcaseFourTitle: "IPgroup - Biotech",
    continueLabel: "Continue",
    skipLabel: "Skip",
  },
};

const cookieBanner = {
  descLineOne: "We use cookies to offer you a better experience. Check",
  linkText: " Cookie Policy ",
  descLineTwo: "for more info.",
  acceptAllBtnText: "Accept all",
  acceptNecessaryBtnText: "Accept only necessary",
};

const bugsnag = {
  errorText: "Bugsnag API key is empty. Bugsnag initialization skipped.",
};

const errorMessages = {
  addToWatchlist: "Added to Watchlist",
  updatedEntryPrice: "Updated entry price",
  watchlistError: "Unable to add to watchlist",
  fieldRequired: "This field is required",
  apiFailed: "Api failed.",
  enterValidEmail: "Please enter valid email.",
  imageUpdateError: "Error updating image.",
  pdfUploadError: "Error uploading PDF",
  validUrl: "Please enter valid url.",
  invalidVideoUrl:
    "Invalid URL. Please make sure you are using the link for a specific video.",
  getMinLengthError: (minLength: number) =>
    `Description should be at least ${minLength} characters long.`,
  getMaxLengthError: (maxLength: number) =>
    `Description should not be more than ${maxLength} characters.`,
  notEmbeddable: "This video is not embeddable. Please use a different video.",
  validNumber: "Please enter a valid number",
  imageSize: "Exceed Maximum size of 1MB",
  invalidImageType: "Invalid image type. Please upload a PNG, JPG or GIF file.",
  videoSource: "URL must be from youtube.com or vimeo.com",
  invalidPassword: "Invalid password",
  visitorNotFound: "Visitor not found.",
  invalidEmailError: "The email field must be a valid email address.",
  emailFieldRequired: "Email is required.",
};

const successMessages = {
  dataUpdatedSuccessfully: "Data updated successfully",
  redirecting: "Redirecting...",
  imageProcessed: "Image processed successfully",
  pdfProcessed: "File uploaded successfully",
  textCopiedSuccessfully: "Text has been copied successfully.",
  draftCreatedSuccessfully: "Draft created successfully",
  pleaseWait: "Please wait...",
  fileDeleteSuccess: "File deleted successfully",
};

const showcase = {
  error: "Something went wrong. Please try again later.",
  createdBy: "Created by Martha Rutherford -",
  curationConnect: "Curation Connect | 2024",
  addToWatchlist: "Added to Watchlist",
  followUs: "Follow us",
  stayUpdated: "Stay Updated",
  hello: "Hello",
  investor: "Investor",
  header: {
    home: "Home",
    keyInfo: "Key info",
    investmentThesis: "Investment thesis",
    experts: "Experts",
    externalInsights: "External insights",
    investorMaterials: "Key materials",
    companyContent: "Company Content",
    media: "Media",
    research: "Research",
    team: "Team",
    followOnX: "Follow on X",
    showcases: "Showcases",
    services: "Services",
    plans: "Plans",
    watchlist: "Watchlist",
  },
  sectionTitles: {
    companyContent: "Company Content",
    keyInfo: "Key Info",
    investmentMethodBold: "Investment Thesis",
    faqs: "What the Pro's Are Asking",
    investorMaterials: "Investor Materials",
    team: "Team",
    letsConnect: "Let's Connect",
    keySocials: "KEY SOCIALS & INFLUENCERS",
    companyResearch: "Company Research",
    articles: "Articles",
    keyArticles: "Research",
    externalInsights: "External Insights",
    experts: "Follow the Experts",
    darkBanner: "Get Deeper Insights",
    darkBannerPrivate: "Contact the company",
    whyInvest: "Why Invest?",
    keyRisk: "Key Risks",
    whyInvestDesc:
      "Key pieces of information about the business that you need to know about.",
    catalystsDesc:
      "The key events that could drive investment opportunities and shift markets.",
  },
  sectionDescriptions: {
    keySocials:
      "Stay connected with what investors, professionals and stakeholders are saying in real time.",
    experts:
      "Quickly navigate key insights from industry experts and leverage their knowledge and market intelligence. ",
    faqSection:
      "Here are the questions that professional investors are asking before making an investment decision.",
    faqSectionPrivate:
      "Here are the questions that institutional investors are asking before making an investment decision.",
    externalInsights:
      "A curated collection of third-party content relevant to the company and sector to help inform your investment decision.",
    investorMaterials:
      "Access the most recent investor updates published by the company.",
    darkBannerDescription:
      "Be kept effortlessly informed with updates and our views on why it matters.",
    darkBannerPrivateDescription:
      "Get more information on the fundraise and connect with the management team.",
  },
  keyInfoTabTitles: {
    executiveSummary: "Executive Summary",
    catalysts: "Catalysts",
    usps: "Why Invest?",
    keyRisk: "Key Risks",
    ceoInterview: "Watch CEO Interview",
    executiveSummaryCTA: "Investor Relations",
  },
  catalystsTabTitles: {
    short: "Near term",
    medium: "Medium term",
    long: "Long term",
  },
  companyContentTabTitles: {
    ipGroup: "IPGroup",
    podcasts: "Podcasts",
  },
  letsConnectTitles: {
    contectUs: "Contact Us",
    completeForm: "Complete the form to contact the investor relations team.",
    completeFormPrivate:
      "Complete this form to learn more about this fundraise.",
    completeFormDAACIPrefix:
      "Complete this form to connect with Simon White, CFO - DAACI (",
    completeFormDAACIEmail: "<EMAIL>",
    completeFormDAACISuffix: ") and learn more about the fundraise.",
  },
  keySocials: {
    follow: "+follow",
  },
  footer: {
    followX: "Follow X",
    contactUs: "Contact Us",
    questions: "Any questions?",
    contact: "Contact: Nirav Karia -",
    email: "<EMAIL>",
    disclaimerHeading: "General disclaimer and copyright",
    disclaimer:
      "This showcase has been commissioned by the Business and prepared and issued by Curation, in consideration of a fee payable by the business. Fees are paid in cash without recourse. Curation may seek additional fees for the provision of content and related IR services for the client but does not get remunerated for any investment banking services.Accuracy of content: All information used in the publication of this showcase has been compiled from publicly available sources that are believed to be reliable, however we do not guarantee the accuracy or completeness of this showcase and have not sought for this information to be independently verified. Opinions contained in this showcase represent those of Curation at the time of publication. Forward-looking information or statements in this report may contain information that is based on assumptions, forecasts of future results, estimates of amounts not yet determinable, and therefore involve known and unknown risks, uncertainties and other factors which may cause the actual results, performance or achievements of their subject matter to be materially different from current expectations.Exclusion of Liability: To the fullest extent allowed by law, Curation shall not be liable for any direct, indirect or consequential losses, loss of profits, damages, costs or expenses incurred or suffered by you arising out or in connection with the access to, use of or reliance on any information contained on this showcase.No personalised advice: The information that we provide should not be construed in any manner whatsoever as, personalised advice. Also, the information provided by us should not be construed by any subscriber or prospective subscriber as Curation's solicitation to effect, or attempt to effect, any transaction in a security. The securities described in the report may not be eligible for sale in all jurisdictions or to certain categories of investors.Investment in securities mentioned: Curation does not itself hold any positions in the securities mentioned in this report, nor the analysts who responsible for compiling this showcase. However, the respective directors, officers, employees and contractors of Curation may have a position in any or related securities mentioned in this showcase, subject to Curation's policies on personal dealing and conflicts of interest.",
    uk: "United Kingdom",
    ukText:
      'This document is prepared and provided by Curation for information purposes only and should not be construed as an offer or solicitation for investment in any securities mentioned or in the topic of this document. A marketing communication under FCA Rules, this document has not been prepared in accordance with the legal requirements designed to promote the independence of investment research and is not subject to any prohibition on dealing ahead of the dissemination of investment research.This Communication is being distributed in the United Kingdom and is directed only at (i) persons having professional experience in matters relating to investments, i.e. investment professionals within the meaning of Article 19(5) of the Financial Services and Markets Act 2000 (Financial Promotion) Order 2005, as amended (the "FPO") (ii) high net-worth companies, unincorporated associations or other bodies within the meaning of Article 49 of the FPO and (iii) persons to whom it is otherwise lawful to distribute it. The investment or investment activity to which this document relates is available only to such persons. It is not intended that this document be distributed or passed on, directly or indirectly, to any other class of persons and in any event and under no circumstances should persons of any other description rely on or act upon the contents of this document.This Communication is being supplied to you solely for your information and may not be reproduced by, further distributed to or published in whole or in part by, any other person.',
    us: "United States",
    usText:
      'Curation relies upon the "publishers\' exclusion" from the definition of investment adviser under Section 202(a)(11) of the Investment Advisers Act of 1940 and corresponding state securities laws. This showcase is a bona fide publication of general and regular circulation offering impersonal investment-related advice, not tailored to a specific investment portfolio or the needs of current and/or prospective subscribers. As such, Curation does not offer or provide personal advice and the research provided is for informational purposes only. No mention of a particular security in this report constitutes a recommendation to buy, sell or hold that or any security, or that any particular security, portfolio of securities, transaction or investment strategy is suitable for any specific person.',
  },
  steps: {
    heroSection: "Hero Section",
    investmentThesis: "Investment Thesis",
    ceoInterview: "Executive Summary",
    experts: "Experts Insights",
    faqDetails: "FAQs",
    externalInsight: "External Insight",
    investorMaterials: "Investor Materials",
    companyResearch: "Company research",
    teamMembers: "Team members",
    disclaimer: "Disclaimer",
    curationAi: "Curation AI",
    discovery: "Discovery Page",
    preview: "Preview",
    publish: "Publish",
  },
  expertsSection: {
    audience: "Audience",
  },
  teamMembers: {
    showMore: "Show more",
    showLess: "Show less",
  },
  articleHeading: "Article",
  reveal: "Reveal",
  discoverNow: "Discover Now — It's Free",
  discoverNowDesc: "Discover new equity stories",
  discoverNowAccess: "Access to our full research suite",
  discoverNowPro: "Get the pro’s validation and insight",
  details: "Details",
  headingRequest: "Request a New Showcase",
  headingRequestDesc: "Let us know which company you want to see.",
  headingRequestDesc1:
    "We'll keep you posted via email when their showcase goes live.",
  viewShowcase: "View Showcase",
  playTeaser: "Play Teaser",
  bullBearHeading: "Bull & Bear Case",
  buyCase: "Bull Case",
  sellCase: "Bear Case",
  bullBearDesc:
    "An overview of the main reasons to invest and the key risks involved.",
  articleReadMore: "Read more",
  subscribeSuccess: "You have successfully subscribed to this showcase.",
  podcastHeading: "Podcast",
  pdfHeading: "PDF",
  download: "Download",
  stockInfo: {
    market: "Market/Symbol",
    price: "Price",
    marketCap: "Market Cap",
    PERatio: "PE Ratio",
    targetPrice: "Price Target",
    averageVolume: "Average Volume",
    raiseAmount: "Raise Amount",
    equityOffered: "Equity Offered",
    valuation: "Valuation (pre-money)",
    revenue: "Revenue",
    marketOpportunity: "Market Opportunity",
    privateShowcaseHelperText:
      "All numbers and prices are accurate as at the time of fundraising.",
    publicShowcaseHelperText: `Pricing delayed 2 hours. {datetime}`,
  },
  stayUpdatedPopup: {
    follow: "Follow",
    curationConnect: "Curation Connect",
    toGet: "To Get",
    buttonLabel: "Stay Updated",
    title: "Stay Updated",
    caption: "Follow Curation Connect to get:",
    important: "Important",
    companyNews: "Company News",
    realTime: "Real-time",
    insightsFromExperts: "Insights from Experts",
    marketAndMacro: "Market and Macro",
    updates: "Updates",
    listItems: [
      "Important company news",
      "Real-time insights from experts",
      "Market and macro updates",
    ],
  },
  lockedSection: {
    membersOnly: "Members-Only Access",
    title: "Unlock our curated investment insights,",
    subtitle: "FREE now and forever.",
    description:
      "Gain free access to all of our insights, content and platform, immediately. Simply sign up with your email.",
    checkPointOneTitle: "Exclusive Insights:",
    checkPointOneDescription:
      "Why invest? What are the bull points and bear points? What are the key catalysts?",
    checkPointTwoTitle: "Market Edge:",
    checkPointTwoDescription:
      "Gain insights that inform and enhance your investment decisions.",
    checkPointThreeTitle: "Early access:",
    checkPointThreeDescription:
      "Be the first to know when showcases are updated, new companies are showcased or when a new research tool & feature is added.",
    checkPointFourTitle: "No Spam Policy:",
    checkPointFourDescription:
      "Your inbox stays clean - only receive updates and content that you want to get.",
    inputPlaceholder: "Enter your email",
    alreadyMember: "Already have an account? Enter your email to log in.",
    stepTwoTitle: "It's great to have you join us!",
    stepTwoDescription:
      "To help us deliver you more tailored content, features and upgrades, please tell us a little more about yourself as an investor.",
    stepTwoInvestorType: "1. What type of investor are you?",
    stepTwoInvestorTypeOptionOne: "Professional investor",
    stepTwoInvestorTypeOptionTwo: "Sophisticated investor",
    stepTwoInvestorTypeOptionThree: "Retail investor",
    stepTwoStocksCategory:
      "2. What category of stocks do you typically invest in?",
    stepTwoStocksCategoryOptionOne: "Growth",
    stepTwoStocksCategoryOptionTwo: "Value",
    stepTwoStocksCategoryOptionThree: "Income",
    stepTwoInvestmentMedium:
      "3. How do you typically invest in stocks & shares?",
    stepTwoInvestmentMediumOptionOne: "Via financial advisor / broker",
    stepTwoInvestmentMediumOptionTwo: "Via managed funds",
    stepTwoInvestmentMediumOptionThree: "Self-invest via trading platforms",
    stepTwoButton: "Continue",
    skip: "Skip",
  },
  copilot: {
    menu: "Menu",
    surfaceInsights: "Surface Insights with Curation AI",
    addCustomPrompt: "Add custom prompt",
    closeCustomPrompt: "Close custom prompt",
    curationAi: "Curation AI",
    questions: "Questions",
    heading: "Quickly surface insights on %company%.",
    promptTimeoutError: "Could not generate response",
    description:
      "From financial data to industry trends, Curation AI can provide the data, analysis and research you need to make more informed decisions.",
    disclaimer:
      "This AI tool can make mistakes and none of the information should be construed as investment advice.",
    disclaimerMobile: "AI can make mistakes. This is not investment advice.",
    inputPlaceholder:
      "Ask me anything, or try some of the suggested questions above!",
    inputPlaceholderMobile: "Try the prompts above!",
    questionOne: "What are the company's revenues?",
    questionTwo: "Is the company profitable?",
    questionThree: "How much cash does it have?",
    questionFour: "Who are the top 5 biggest shareholders?",
    questionFive: "Tell me about the CEO's background",
    questionSix: "Who are the major competitors?",
    questionSeven: "What is the market opportunity?",
    promptError: "Curation AI is busy at the moment.",
    skipAnimation: "Load response",
  },
};

const letsConnect = {
  firstName: "First name",
  lastName: "Last name",
  email: "Email",
  role: "Role",
  investerType: "Investor Type",
  message: "Message",
  emailPlaceholder: "Email address",
  submitButtonLabel: "Send message",
  iAgree: "I agree to the",
  terms: "Terms & Conditions",
  acknowledge: "and acknowledge the",
  privacyPolicy: "Privacy Policy",
  errorSubmitting: "Error while Submitting details.",
  detailsSubmitted: "Details submitted successfully.",
  youCanConnect: "You can also email us at",
  save: "Save",
  followSocials: "Follow our socials",
  followSocialsDesc:
    "We post updates and content every day on the companies we showcase. Follow to be sure you don't miss anything!",
  joinList: "Join our mailing list",
  joinListDesc: "To keep up-to-date with the companies showcased",
  subscribe: "Subscribe",
  youtube: "Youtube",
  linkedIn: "LinkedIn",
  twitter: "X/Twitter",
};

const footer = {
  copyright: "Copyright © 2024 Showcase Inc.",
};

const deleteModal = {
  title: "Delete Confirmation",
  description:
    "Are you sure you want to delete this item? This cannot be undone.",
  confirm: "Yes, Delete",
  cancel: "No, Cancel",
};

const lightBanner = {
  get: "Get",
  realtime: "real time",
  updates: "updates",
  news: "Company News",
  insights: "Expert insight",
  MarketUpdates: "Market news",
  contactThe: "Contact the",
  contactCompany: "company",
  meetManagement: "Meet management",
  fundraise: "Fundraise information",
  dataRoomAccess: "Data room access",
};

const defaultCTA = "Follow the Stock";
const defaultPrivateCTA = "Contact";

const bannerTypes = {
  light: "Light",
  dark: "Dark",
};

const confirmation = {
  yes: "Yes",
  no: "No",
};

const device = {
  mobile: "mobile",
  desktop: "desktop",
  mobileBreakpointWidth: 768,
};

const videoSources = {
  youtube: "youtube",
  youtubeShort: "youtu.be",
  vimeo: "vimeo",
  yotubePlayer: "https://www.youtube.com/embed",
  vimeoPlayer: "player.vimeo.com/video",
  vimeoDomain: "vimeo.com",
};
const socialSignin = {
  signinWith: "Sign in with %s",
  signupwith: "Continue with %s",
};

const comingSoon = {
  heading: "Welcome to the community",
  subHeading:
    "{company} is coming soon, we will notify you when it goes live on the platform.",
  discoverMoreButton: "Discover Unique Equity Stories",
  subscribeButton: "Subscribe",
};

const showcaseStatusTypes = {
  draft: "draft",
  pending: "pending",
  live: "live",
  archived: "archived",
  inProgress: "in-progress",
};
export const lang = {
  header,
  footer,
  dashboard,
  home,
  signUp,
  verifyOtp,
  signIn,
  auth,
  forgotPassword,
  resetPassword,
  passwordCreate,
  signUpSuccess,
  confirmEmailSuccess,
  signUpVerification,
  settingsNavigation,
  settings,
  dropdown,
  modal,
  cropperModal,
  confirmationModal,
  removeProfileImageModal,
  flyoutMenu,
  notifications,
  payments,
  team,
  teams,
  teamSettings,
  responseErrorMessages,
  signupVerificationExpired,
  resetPasswordExpired,
  support,
  boundaryError,
  inviteMemberModal,
  deleteUserModal,
  deleteInvitedUserModal,
  editUserModal,
  onboarding,
  cookieBanner,
  bugsnag,
  verifyAccount,
  navigation,
  leaveTeamModal,
  leaveTeamDeniedModal,
  deleteTeamModal,
  createTeamModal,
  toggleButton,
  errorMessages,
  successMessages,
  showcase,
  letsConnect,
  buttonLabels,
  deleteModal,
  lightBanner,
  defaultCTA,
  defaultPrivateCTA,
  bannerTypes,
  videoSources,
  socialSignin,
  confirmation,
  device,
  comingSoon,
  showcaseStatusTypes,
};

export default lang;
