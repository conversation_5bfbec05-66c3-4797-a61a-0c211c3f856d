
import { fetcher } from "@/common/utils/network/baseFetcher";
import { httpRequestMethods } from "@/common/utils/network/constants";
import { showcasePublicURL } from "@/common/utils/network/endpoints";
import useSWRMutation from "swr/mutation";
import { handleFetchError } from "@/common/utils/network/errorHandler";
import { fetchStockQuote } from "../company/[slug]/fetchShowcase";

const { GET } = httpRequestMethods;

const showcaseFetcher = async (key: string, { arg } : { arg: { slug: string }}) => {
  return fetcher(key.replace('{slug}', arg.slug), {
    arg: {
      method: GET,
    },
  });
};

export const useGetShowcase = () => {
  const {
    trigger,
    isMutating,
  } = useSWRMutation(showcasePublicURL, showcaseFetcher);

  const onFetchShowcase = async (slug: string) => {
    try {
      const res = await trigger({
        slug,
      });
      if (res.data) {
        try {
          const stockData = await fetchStockQuote(slug);
          return {
            ...res.data,
            stock_price: stockData.price,
          };
        } catch (error) {
          console.error("Error fetching stock price:", error);
          return res.data;
        }
      }
    } catch (error) {
      handleFetchError(error)
    }
  }

  return {
    onFetchShowcase,
    isFetchingShowcase: isMutating,
  }
}
  