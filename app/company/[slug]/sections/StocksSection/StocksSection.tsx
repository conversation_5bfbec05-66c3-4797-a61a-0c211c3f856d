'use client';

import StockInfo from "@/app/company/[slug]/sections/StocksSection/StockInfo";
import lang from "@/common/lang";
import dayjs from "dayjs";
import { formatStockPrices } from "@/common/utils/helpers";
import {
  motion,
} from "framer-motion";
import SectionLayout from "../../sectionLayout";
import { useTickersList } from "../../hooks/useTickersList";
import PriceTarget from "./PriceTarget";

type Props = {
  privateShowcase: boolean;
  currencySymbol: string;
  raiseAmount: string;
  raiseAmountCurrency: string;
  equityOffered: string;
  valuation: string;
  valuationCurrency: string;
  revenue: string;
  revenueCurrency: string;
  marketOpportunity: string;
  marketOpportunityCurrency: string;
  exchange: string;
  symbol: string;
  price: string;
  changePercent: string;
  marketCap: string;
  PERatio: string;
  averageVolume: string;
  targetPrice: number;
  targetPriceChange?: number;
  isGoogleFinanceEnabled: boolean;
  stockSymbol: string;
  priceTargetDisclaimer: string;
}

const StocksSection = ({
  privateShowcase, raiseAmount, currencySymbol, stockSymbol, isGoogleFinanceEnabled, targetPriceChange, targetPrice, raiseAmountCurrency, priceTargetDisclaimer, equityOffered, valuation, valuationCurrency, revenue, revenueCurrency, marketOpportunity, marketOpportunityCurrency, exchange, marketCap, changePercent, symbol, PERatio, price, averageVolume,
}: Props) => {
  const { showcase: { stockInfo } } = lang;

  const formattedRaiseAmount = formatStockPrices(raiseAmount);
  const formattedValuation = formatStockPrices(valuation);
  const formattedRevenue = formatStockPrices(revenue);
  const formattedMarketOpportunity = formatStockPrices(marketOpportunity);
  const formattedMarketCap = formatStockPrices(marketCap);
  const formattedAverageVolume = formatStockPrices(averageVolume);
  const averageVolumeValue = parseInt(formattedAverageVolume.value) || 'N/A';
  const revenueValue = parseInt(formattedRevenue.value) ? `${revenueCurrency}${formattedRevenue.value}` : 'N/A';
  const {
    tickersList,
  } = useTickersList(); 

  const formatPriceWithCurrency = (price: string, exchange: string) => {
    if (!isGoogleFinanceEnabled) {
      return `${currencySymbol}${price}`
    }
    if (!tickersList) { return price; }
    
    let withCurrency = '';
    tickersList.forEach((ticker) => {
      if (ticker.exchange_symbol === exchange) {
        withCurrency = `${ticker.currency_symbol}${price}`;
      }
    });
    return withCurrency;
   
  }

  return (
    <SectionLayout id="stocks">
      <motion.div
        initial={{
          opacity: 0,
          y: 10,
        }}
        animate={{
          opacity: 1,
          y: 0,
        }}
        transition={{
          type: "spring",
          delay: 2,
        }}
        className="flex rounded-lg relative z-[5] overflow-hidden flex-col items-start gap-2 w-full py-3 md:py-4 px-4 md:px-8 bg-[#1B1A1F80] border border-[#FFFFFF15] border-b-0"
      >
        <div className="w-1/2 h-[140px] left-1/2 absolute -translate-x-1/2 top-0 -translate-y-1/2 bg-[#3F3D42] blur-[80px] rounded-full -z-10"></div>
        <div className="flex w-full items-center justify-between gap-3 flex-wrap md:flex-nowrap">
          {
            privateShowcase ? (
              <>
                <StockInfo title={stockInfo.raiseAmount} value={`${raiseAmountCurrency}${formattedRaiseAmount.value}`} unit={formattedRaiseAmount.unit} />
                <StockInfo title={stockInfo.equityOffered} value={equityOffered} unit="%" />
                <StockInfo title={stockInfo.valuation} value={`${valuationCurrency}${formattedValuation.value}`} unit={formattedValuation.unit} />
                <StockInfo title={stockInfo.revenue} value={revenueValue} unit={formattedRevenue.unit} />
                <StockInfo title={stockInfo.marketOpportunity} value={`${marketOpportunityCurrency}${formattedMarketOpportunity.value || 0}`} unit={formattedMarketOpportunity.unit} />
              </>
            ) : (
              <>
                <StockInfo title={stockInfo.market} value={`${isGoogleFinanceEnabled ? `${exchange || 'N/A'}:${symbol || 'N/A'}` : stockSymbol}`} />
                <StockInfo title={stockInfo.price} value={formatPriceWithCurrency(price, exchange)} targetPriceChange={parseFloat(changePercent)} />
                {targetPrice ? (
                  <PriceTarget priceTargetDisclaimer={priceTargetDisclaimer} title={stockInfo.targetPrice} targetPriceChange={targetPriceChange} value={formatPriceWithCurrency(targetPrice.toString(), exchange)} />
                ) : null}
                <StockInfo title={stockInfo.marketCap} value={formatPriceWithCurrency(formattedMarketCap.value, exchange)} unit={formattedMarketCap.unit} />
                <StockInfo title={stockInfo.PERatio} value={PERatio || 0} />
                <StockInfo title={stockInfo.averageVolume} value={averageVolumeValue } unit={formattedAverageVolume.unit} />
              </>
            )
          }
        </div>

        <p className="text-xs font-normal text-[#BAB9BD]">
          { privateShowcase ? stockInfo.privateShowcaseHelperText : stockInfo.publicShowcaseHelperText.replace('{datetime}', dayjs().format('MMM D, YYYY h:00 A')) }
        </p>
      </motion.div>
    </SectionLayout>
  );
}

export default StocksSection;
