'use client';

import { useState } from "react";
import dynamic from "next/dynamic";
import {
  motion,
} from "framer-motion";
import { VideoTab } from "./types";
import HeroBuySell from "./HeroBuySell";

const HeroVideo = dynamic(() => import("./HeroVideo").then(m => m.HeroVideo), { ssr: false });
const HeroStock = dynamic(() => import("./HeroStock").then(m => m.HeroStock), { ssr: false });

type Props = {
  videoSrc: string;
  videoLabel: string;
  isGoogleFinanceEnabled: boolean;
  slug: string;
  teaserVideoTitle: string;
  teaserVideoCaption: string;
  exchange: string;
  symbol: string;
  discoveryCoverImage?: string;
  graphLink: string;
  price: string;
  changePercent: string;
  stockSymbol: string;
  currencySymbol: string;
  buy: Array<{
    title: string;
    caption: string;
  }>;
  sell: Array<{
    title: string;
    caption: string;
  }>;
}

const ThreeTabLayout = ({
  videoSrc, videoLabel, stockSymbol, currencySymbol, isGoogleFinanceEnabled, slug, discoveryCoverImage, graphLink, teaserVideoCaption, teaserVideoTitle, exchange, symbol, buy, sell, price, changePercent,
}: Props) => {
  const [activeTab, setActiveTab] = useState(VideoTab)
  return (
    <motion.div
      layout
      initial={{
        opacity: 0,
        y: 100,
      }}
      animate={{
        opacity: 1,
        y: 0,
      }}
      exit={{
        opacity: 0,
        y: 100,
      }}
      transition={{
        duration: 2,
        delay: 1,
      }}
      className={`mt-10 lg:mt-12 flex flex-col lg:flex-row gap-4 lg:min-h-[495px] lg:max-h-[600px] mb-10 lg:mb-16`}>
      <HeroVideo
        activeTab={activeTab}
        discoveryCoverImage={discoveryCoverImage}
        videoSrc={videoSrc}
        slug={slug}
        videoLabel={videoLabel}
        setActiveTab={setActiveTab}
        ifOnlyVideo={buy?.length === 0 && sell?.length === 0 && !graphLink}
        teaserVideoCaption={teaserVideoCaption}
        teaserVideoTitle={teaserVideoTitle}
      />
      {buy?.length && sell?.length ? (
        <HeroBuySell
          activeTab={activeTab}
          setActiveTab={setActiveTab}
          buy={buy}
          slug={slug}
          sell={sell}
        />
      ) : null}
      {graphLink || (exchange && symbol) ? (
        <HeroStock
          activeTab={activeTab}
          stockSymbol={stockSymbol}
          currencySymbol={currencySymbol}
          setActiveTab={setActiveTab}
          isGoogleFinanceEnabled={isGoogleFinanceEnabled}
          slug={slug}
          exchange={exchange}
          symbol={symbol}
          price={price}
          changePercent={changePercent}
          graphLink={graphLink}
        />
      ) : null}
    </motion.div>
  )
}

export default ThreeTabLayout;
