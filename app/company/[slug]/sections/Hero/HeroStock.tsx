'use client';

import {
  useState, useEffect,
} from "react";
import { HeroStockIcon } from "@/common/components/icons";
import {
  motion, AnimatePresence,
} from "framer-motion";
import dynamic from "next/dynamic";
import { useWindowDimensions } from "@/common/hooks";
import { useTickersList } from "../../hooks/useTickersList";
import { useStockQuote } from "../../hooks/useStockQuote";
import {
  mixpanelCustomEvent,
  MixpanelEventName,
} from "@/common/utils/mixpanel/eventTriggers";
import Image from "next/image";
import Spinner from "@/common/components/molecules/loader/spinner";
import { StockTab } from "./types";
import { RangeSelector } from "./RangeSelector";
import { useChartData } from "./useChartData";
import dayjs from "dayjs";

const Chart = dynamic(() => import("./Chart").then(m => m.Chart), { ssr: false });

type Props = {
  activeTab: string;
  setActiveTab: (value: string) => void;
  slug: string;
  exchange?: string;
  symbol?: string;
  graphLink: string;
  price: string;
  changePercent: string;
  currencySymbol: string;
  stockSymbol: string;
  isGoogleFinanceEnabled: boolean;
};

export const HeroStock = ({
  activeTab,
  setActiveTab,
  stockSymbol,
  isGoogleFinanceEnabled,
  currencySymbol,
  slug,
  price: fallbackPrice,
  changePercent: fallbackChangePercent,
  exchange = '',
  symbol = '',
  graphLink,
}: Props) => {
  const isActive = activeTab === StockTab;
  const { windowSize } = useWindowDimensions();
  const [activeRange, setActiveRange] = useState('1M');
  const {
    isLoading: isQuoteLoading,
    changeInPrice,
    changePercent,
    chartPrice,
    latestDate,
    oneMonthCompareDate,
    threeMonthCompareDate,
    sixMonthCompareDate,
    ytdCompareDate,
    yearCompareDate,
  } = useStockQuote(slug, activeRange);

  const {
    chartData, isLoading: isChartLoading,
  } = useChartData({
    slug,
    activeRange,
    oneMonthCompareDate,
    threeMonthCompareDate,
    sixMonthCompareDate,
    ytdCompareDate,
    yearCompareDate,
  });
  const {
    tickersList,
  } = useTickersList();

  const price = chartPrice || fallbackPrice;
  const rangeChangePercent = changePercent || fallbackChangePercent;
  const rangeChangePrice = changeInPrice || 0;
  const isLoading = isChartLoading || isQuoteLoading;

  const formatPriceWithCurrency = (price: string, exchange: string) => {
    if (!isGoogleFinanceEnabled) {
      return `${currencySymbol}${price}`
    }
    if (!tickersList) { return price; }

    let withCurrency = '';
    tickersList.forEach((ticker) => {
      if (ticker.exchange_symbol === exchange) {
        withCurrency = `${ticker.currency_symbol}${price}`;
      }
    });
    return withCurrency;

  }
  const improvedGraphLink = (() => {
    try {
      const url = new URL(graphLink);
      const params = new URLSearchParams(url.search);
      params.set("graphColor", "8E4FFB");
      params.set("fontFamily", "Montserrat");
      params.set("backgroundColor", "transparent");
      params.set("textColor", "b3b1ab");
      url.search = params.toString();
      return url.toString();
    } catch (e) {
      return graphLink;
    }
  })();

  useEffect(() => {
    if (isActive && windowSize === 'mobile') {
      const scrollTimeout = setTimeout(() => {
        const element = document.getElementById("hero-stock");
        if (element) {
          element.style.scrollMarginTop = '100px';
          element.scrollIntoView({ behavior: 'smooth' });
        }
      }, 800);

      return () => clearTimeout(scrollTimeout);
    }
  }, [isActive, windowSize]);

  const activeVariants = {
    hidden: windowSize === "mobile" ? { opacity: 0 } : {
      opacity: 0,
      scale: 0,
    },
    visible:
      windowSize === "mobile"
        ? {
          opacity: 1,
          transition: {
            duration: 0.8,
            ease: "easeIn",
          },
        }
        : {
          opacity: 1,
          scale: 1,
          transition: {
            duration: 0.8,
            ease: "easeIn",
          },
        },
  };

  const inactiveVariants = {
    hidden:
      windowSize === "mobile"
        ? {
          opacity: 0,
          transition: {
            duration: 0.4,
            ease: "easeOut",
          },
        }
        : {
          opacity: 0,
          scaleY: 0,
          transition: {
            duration: 0.4,
            ease: "easeOut",
          },
        },
    visible:
      windowSize === "mobile"
        ? {
          opacity: 1,
          transition: {
            duration: 0.4,
            ease: "easeIn",
          },
        }
        : {
          opacity: 1,
          scaleY: 1,
          transition: {
            duration: 0.4,
            ease: "easeIn",
          },
        },
  };

  const containerVariants = {
    active: {
      flexGrow: 1,
      flexBasis: "0%",
      flexShrink: 1,
      transition: {
        duration: 0.8,
        ease: "easeInOut",
      },
    },
    inactive: {
      flexGrow: 0,
      flexBasis: "auto",
      flexShrink: 0,
      transition: {
        duration: 0.8,
        ease: "easeInOut",
      },
    },
  };

  return (
    <motion.div
      variants={containerVariants}
      initial="inactive"
      animate={isActive ? "active" : "inactive"}
      exit="inactive"
      id="hero-stock"
    >
      <div
        className={`rounded-md border border-white border-opacity-10 h-full ${isActive ? "" : "hover:bg-white/10 hover:-translate-y-4 transition-transform ease-in-out duration-300"}`}
      >
        <AnimatePresence mode="wait">
          {isActive && (
            <motion.div
              key="active-content"
              variants={activeVariants}
              initial="hidden"
              animate="visible"
              exit="hidden"
              style={{ transformOrigin: "100% 0" }}
              className="rounded-md border sm:min-h-auto min-h-[490px] border-white border-opacity-10 h-full justify-center items-center flex overflow-hidden relative gap-12 flex-col p-4 lg:py-6 lg:px-8"
            >
              <div className="-z-10 h-[500px] lg:hidden w-[700px] absolute -right-24 -top-24 bg-gradient-to-tr blur-[120px] from-[#30096F00] opacity-40 via-[#AD7EFF76] to-[#00000015] -rotate-[25deg]"></div>
              <div className="-z-10 h-[250px] lg:hidden w-full absolute left-20 opacity-55 -top-20 bg-gradient-to-b from-white to-black blur-[140px] "></div>
              <div className="-z-10 h-[120px] lg:hidden w-[700px] absolute -right-40 top-[250px] bg-gradient-to-tr blur-[120px] bg-[#AD7EFF76] -rotate-[25deg]"></div>
              <div className="-z-10 h-[120px] lg:block hidden w-[700px] absolute -left-[120px] top-12 bg-gradient-to-tr blur-[80px] from-[#01002A00] via-[#AD7EFF76] to-[#FFFFFF76] -rotate-[25deg]"></div>
              <div className="-z-20 h-[300px] lg:block hidden w-[700px] absolute -right-[200px] top-12 bg-gradient-to-br blur-[120px] from-[#30096F00] via-[#AD7EFF76] to-[#00000015] rotate-[25deg]"></div>
              <div className="-z-20 h-[300px] lg:block hidden w-[700px] absolute -left-[200px] bottom-12 bg-gradient-to-br blur-[120px] from-[#30096F00] via-[#AD7EFF26] to-[#00000015] rotate-[25deg]"></div>
              {isLoading ? (
                <div className="absolute inset-0 flex items-center justify-center  backdrop-blur-sm rounded-md z-10">
                  <Spinner />
                </div>
              ) : (
                chartData[0]?.data?.length > 0 ? (
                  <div className="w-full flex gap-6 flex-1 lg:flex-row flex-col">
                    <div className="flex-1">
                      <div className="flex flex-col items-start">
                        <div className="text-[#BAB9BD] text-sm lg:text-base capitalize">{isGoogleFinanceEnabled ? `${exchange.toLowerCase()} • ${symbol}` : `${stockSymbol.split(':').join(' • ')}`}</div>
                        <div className="mt-1 text-white lg:text-2xl flex-1 font-medium">{formatPriceWithCurrency(Number(price).toFixed(2), exchange)} <span className={`${activeRange === "All" && "hidden"} ml-1 lg:ml-2 ${rangeChangePrice > 0 ? "text-[#BB95FD]" : "text-red-600"} font-medium text-xs lg:text-base`}>{rangeChangePrice} ({rangeChangePercent}%)</span></div>
                        {activeRange !== "All" && <div className="mt-1 text-[#737373] text-xs">Close Time: {dayjs(latestDate).format('MMM D, YYYY')}</div>}
                      </div>
                      <div className="relative min-h-[300px]">
                        {isLoading && (
                          <div className="absolute inset-0 flex items-center justify-center backdrop-blur-sm rounded-md z-10">
                            <Spinner />
                          </div>
                        )}
                        {chartData[0].data?.length > 0 && !isLoading && <Chart exchange={exchange} formatPriceWithCurrency={formatPriceWithCurrency} chartData={chartData} activeRange={activeRange} />}
                      </div>
                      <div className="border-t flex lg:hidden border-[#3F3D42] justify-between items-center">
                        <RangeSelector activeRange={activeRange} setActiveRange={setActiveRange} isLoading={isLoading} slug={slug} />
                      </div>
                    </div>
                  </div>
                ) : (
                  slug === "All-Things-Considered-26125" ? (
                    <Image
                      src="/static/stock_atc.png"
                      width={1200}
                      height={800}
                      className="w-full h-auto"
                      alt="stock"
                    />
                  ) : slug === "Archer-Limited-25642" ? (
                    <Image
                      src="/static/stock_archer.png"
                      width={1200}
                      height={800}
                      quality={100}
                      className="w-full h-auto"
                      alt="stock"
                    />
                  ) : (
                    <iframe
                      className="w-full h-[475px] rounded-[10px]"
                      src={improvedGraphLink}
                      title="Zika graph"
                    />
                  )
                ))}
              {chartData[0]?.data?.length > 0 && !isLoading && (
                <div className="w-full border-t lg:flex hidden border-[#3F3D42] justify-between items-center">
                  <RangeSelector activeRange={activeRange} setActiveRange={setActiveRange} isLoading={isLoading} slug={slug} />
                  <div className="text-[#737373] text-xs">Pricing accurate to close of selected period</div>
                </div>
              )}
            </motion.div>
          )}
          {!isActive && (
            <motion.button
              key="inactive-button"
              variants={inactiveVariants}
              initial="hidden"
              animate="visible"
              exit="hidden"
              style={{ transformOrigin: "0 0" }}
              type="button"
              onClick={() => {
                setActiveTab(StockTab);
                mixpanelCustomEvent({
                  eventName: MixpanelEventName.heroSectionOpened,
                  mixpanelProps: {
                    page: "Showcase",
                    tab: StockTab,
                    slug: slug,
                  },
                });
              }}
              className="rounded-md w-full outline-none overflow-hidden h-full py-3 lg:py-6 px-6 lg:px-12 flex lg:flex-col justify-between relative"
            >
              <div className="h-[150px] -z-20 block lg:hidden w-full absolute -left-40 bottom-0 blur-[40px] bg-gradient-radial opacity-50 from-[#AD7EFF] to-black"></div>
              <div className="h-[60px] hidden lg:block w-[500px] absolute left-1/2 -translate-x-1/2 top-16 blur-[60px] bg-[#AD7EFF] -rotate-45"></div>
              <div className="text-white lg:text-xl text-left lg:pl-8 font-medium lg:absolute top-[calc(13%)] lg:w-[170px] lg:rotate-90 lg:left-1/2 lg:-translate-x-1/2 whitespace-nowrap">
                Stock Chart
              </div>
              <div></div>
              <div>
                <HeroStockIcon />
              </div>
            </motion.button>
          )}
        </AnimatePresence>
      </div>
    </motion.div>
  );
};
