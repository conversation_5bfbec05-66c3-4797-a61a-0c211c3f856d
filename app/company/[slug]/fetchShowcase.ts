import {
  showcasePublicURL,
  stockQuoteUrl,
} from "@/common/utils/network/endpoints";

export type StockQuoteResponse = {
  [date: string]: {
    price: string;
  };
};

export const fetchStockQuote = async (slug: string) => {
  const url = stockQuoteUrl.replace("{slug}", slug);

  try {
    const res = await fetch(url);
    const data: StockQuoteResponse = await res.json();

    const dateKeys = Object.keys(data).filter((key) => key !== "code");
    const latestDate = dateKeys.length > 0 ? dateKeys[0] : null;
    const latestQuoteData = latestDate && data ? data[latestDate] : null;

    return {
      price: latestQuoteData?.price || 0,
    };
  } catch (error) {
    console.error("Stock quote fetch error:", error);
    throw error;
  }
};


export const fetchShowcase = async (slug: string, password: string = "") => {
  let url = showcasePublicURL.replace("{slug}", slug);
  if (password) {
    url += `?showcase_token=${password}`;
  }

  try {
    const res = await fetch(url, { next: { tags: ["showcase"] } });
    const stockData = await fetchStockQuote(slug);
    const showcaseData = await res.json()
    return {
      data: {
        ...showcaseData.data,
        stock_price: stockData.price,
      },
      message: showcaseData.message,
    };
  } catch (error) {
    console.error(error);
    throw error;
  }
};
