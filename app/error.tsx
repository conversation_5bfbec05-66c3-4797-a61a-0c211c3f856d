'use client'
import { useEffect } from 'react'
import { But<PERSON> } from "@/common/components/atoms";
import lang from "@/common/lang";
import Bugsnag from '@bugsnag/js';
import { useRouter } from 'next/navigation';
import { LogoIcon } from '@/common/components/icons';

const { boundaryError } = lang;

type ErrorProps = {
  error: Error;
};

// This error boundary is used to catch errors in the React tree below it.
// It won't catch errors in async code, e.g. in `useEffect` or server side rendering and event handlers.

export default function Error ({
  error,
}: ErrorProps) {
  const router = useRouter()
  useEffect(() => {
    Bugsnag.notify({
      name: error.name,
      message: error.message,
    })
    console.error(error)
  }, [error])

  return (
    <div className="grid h-screen px-4 bg-[#0E0E0F] place-content-center">
      <div className="flex flex-col items-center">
        <div className='scale-125'>
          <LogoIcon />
        </div>
        <h1 className="font-bold mt-4 text-gray-200 text-4xl mb-2">{boundaryError.title}</h1>
        <p className="text-center max-w-md font-medium tracking-tight text-gray-400 mt-2 mb-10">
          {boundaryError.description}
          <b><EMAIL></b>
        </p>
        <Button
          width="auto"
          variant="primary"
          size="md"
          type="button"
          onClick={() => router.refresh()}
        >
          {boundaryError.buttonText}
        </Button>
      </div>
    </div>
  )
}
