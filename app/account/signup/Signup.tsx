'use client';

import {
  useEffect, useState,
  useRef,
} from 'react';
import {
  useForm, Controller,
} from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import { Toaster } from "@/common/components/molecules/toast/newToast";
import {
  Input, Loader,
} from '@/common/components/molecules';
import lang from '@/common/lang';
import type {
  SubscriberSignUpDTO,
} from '../../cms/types';
import { signupValidationSchema } from './validationSchema';
import {
  Button, Typography, Link,
} from "@/common/components/atoms";
import { routes } from "@/common/routes";
import { useSignup } from "@/app/account/signup/useSignup";
import { SocialWrapper } from "@/common/components/organisms/socialWrapper";
import Image from 'next/image';
import {
  privacyPolicyUrl, termsUrl,
} from '@/common/constants';
import { Info } from './info';
import MixpanelPageView from '../MixpanelPageView/MixpanelPageView';
import { useSearchParams } from "next/navigation"
import { useWindowDimensions } from '@/common/hooks';

const {
  signUp: signUpCopy,
} = lang;


const Signup = ({
  password,
}: {
  password: string;
}) => {
  const searchParams = useSearchParams();
  const showcase = searchParams.get('showcase');
  const [showFirstIteration, setShowFirstIteration] = useState(true);
  const {
    isLoading, onSubmit,
    logSignupInfoMixpanel,
    providers,
  } = useSignup({
    password,
  });
  const firstRender = useRef(false);

  const {
    windowSize,
  } = useWindowDimensions()
 
  useEffect(() => {
    const signupInfoType = async () => {
      if (showcase) {
        logSignupInfoMixpanel(true);
      } else {
        logSignupInfoMixpanel(false);
      }
    }

    if (!firstRender.current) {
      signupInfoType();
      firstRender.current = true;
    }
  }, [logSignupInfoMixpanel, showcase]);

  useEffect(() => {
    if (showcase) {
      setShowFirstIteration(true)
      sessionStorage.setItem('redirect', `${window.location.origin}/company/${showcase}`);
    } else {
      setShowFirstIteration(true)
    }
  }, [showcase]);

  const {
    handleSubmit,
    control,
    formState: {
      isValid,
    },
  } = useForm<SubscriberSignUpDTO>({
    resolver: yupResolver(signupValidationSchema),
    mode: 'onSubmit',
  });

  return (
    <div className="min-h-screen flex">
      <MixpanelPageView title='Sign up | Investor' />
      {isLoading && <Loader />}
      <div className="flex flex-1 justify-center pt-12 sm:pt-6 sm:items-center bg-[#0a0a0b] px-6 sm:px-8 py-6">
        <div className="w-full flex items-center flex-col">
          <div className="w-full sm:w-[440px] flex items-center flex-col">
            <Image src="/static/CurationConnectLogo.svg" alt="logo" width="100" height="40" className="h-[36px] w-auto" />
            <Typography variant="h1" className={`mt-12 mb-3 font-semibold text-3xl text-center text-white`}>
              {showFirstIteration ? signUpCopy.headerRegister : signUpCopy.headerRegisterWatchList}
            </Typography>
            <Typography variant="h2" className={`${showFirstIteration ? "" : "sm:block hidden"} max-w-[420px] text-sm text-center mb-5 sm:mb-5 !text-[#b9b8bc]`}>
              {showFirstIteration && windowSize === "mobile" ? signUpCopy.registerDescriptionMobile : signUpCopy.registerDescription}
            </Typography>
            <div className="w-full flex flex-col items-center">
              <div>
                <Typography variant="span" className="!text-header-link-item text-sm">
                  {signUpCopy.accountExists}
                </Typography>
                {' '}
                <Link variant="primary" href={routes.showcaseLogin} className='!text-[#BB95FD] font-medium hover:!text-[#BB95FD] hover:opacity-80'>
                  {signUpCopy.login}
                </Link>
              </div>
            </div>
            <Typography
              variant="h4"
              className={`text-sm !text-[#BAB9BD] mt-10 sm:hidden`}
            >
              {signUpCopy.info.disclaimer}
            </Typography>
            <form className="w-full mt-12" onSubmit={handleSubmit(onSubmit)}>
              <div className="flex">
                <Controller
                  control={control}
                  name="email"
                  render={({
                    field, fieldState,
                  }) => {
                    const { error } = fieldState;
                    const {
                      ref, ...fieldProperties
                    } = field;
                    return (
                      <Input
                        width="w-full"
                        id={field.name}
                        type="email"
                        data-cy="email"
                        placeholder='Enter email address'
                        labelText={""}
                        error={!!error}
                        errorMessage={error?.message}
                        classname="font-normal !bg-dark-border-custom focus:!ring-1 !placeholder-[#A3A3A3] text-white px-4 !ring-0 focus:ring-white hover:ring-white custom-input !rounded-lg"
                        {...fieldProperties}
                      />
                    );
                  }}
                />
              </div>
              <div className="mt-6">
                <Button
                  type="submit"
                  variant="white"
                  size="md"
                  disabled={!isValid}
                  data-cy="submit-button"
                  className="hover:bg-white/90"
                  width="w-full"
                >
                  {signUpCopy.submitButtonLabel}
                </Button>
              </div>
            </form>
            {providers?.length !== 0 && <SocialWrapper providers={providers} />}
          </div>
          <div className='text-xs text-[#9A989E] w-full sm:w-[440px] text-center mt-4'>
            <span>
              {signUpCopy.termsDesc1}
            </span>
            <Link variant="primary" target='_blank' href={termsUrl} className='!text-[#9A989E] underline px-1 font-medium text-xs hover:!text-white'>
              {signUpCopy.termsLabel}
            </Link>
            <span>
              {signUpCopy.termsDesc2}
            </span>
            <Link variant="primary" target='_blank' href={privacyPolicyUrl} className='!text-[#9A989E] underline font-medium px-1 text-xs hover:!text-white'>
              {signUpCopy.privacyPolicyLabel}
            </Link>
          </div>
        </div>
      </div>
      {showFirstIteration ? <Info /> : null}
      <Toaster />
    </div>
  );
};

export default Signup;
