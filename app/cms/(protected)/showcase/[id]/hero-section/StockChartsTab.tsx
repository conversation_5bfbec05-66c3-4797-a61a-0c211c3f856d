import SectionsLayout from "@/app/cms/(protected)/showcase/[id]/SectionsLayout";
import lang from "@/common/lang";
import { SearchableSelectBox } from "@/common/components/atoms";
import { ManageLinksFormDataType } from "@/app/cms/(protected)/showcase/[id]/types";
import {
  Control, Controller, UseFormClearErrors, UseFormSetError, UseFormSetValue, UseFormGetValues, useWatch,
} from "react-hook-form";
import {
  Input,
} from "@/common/components/molecules";
import { useSearchSymbols } from "./useSearchSymbols";

type Props = {
  control: Control<ManageLinksFormDataType>,
  setValue: UseFormSetValue<ManageLinksFormDataType>;
  setError: UseFormSetError<ManageLinksFormDataType>;
  clearErrors: UseFormClearErrors<ManageLinksFormDataType>;
  getValues: UseFormGetValues<ManageLinksFormDataType>;
  isGoogleFinanceEnabled: boolean;
};
export const StockChartsTab = ({
  control, isGoogleFinanceEnabled,
}: Props) => {
  const {
    dashboard: { showcase },
  } = lang;
  const selectedExchange = useWatch({
    control,
    name: "exchange",
    defaultValue: "",
  });
  const {
    searchTerm,
    setSearchTerm,
    options: symbolOptions,
    isLoading: isSearchingSymbols,
  } = useSearchSymbols(selectedExchange);
  return (
    <SectionsLayout title={showcase.stocksSectionTitle}>
      <div className="flex flex-col gap-6">
        {!isGoogleFinanceEnabled && (
          <SearchableSelectBox
            control={control}
            fieldName="stockSymbol"
            label={showcase.searchSymbolLabel}
            placeholder={showcase.searchSymbolPlaceholder}
            options={symbolOptions}
            isLoading={isSearchingSymbols}
            searchValue={searchTerm}
            onSearchChange={setSearchTerm}
          />
        )}
        <Controller
          name="graphLink"
          control={control}
          render={({
            field, fieldState,
          }) => {
            const { error } = fieldState;
            return (
              <Input
                width="w-full"
                id={field.name}
                error={!!error}
                errorMessage={error?.message}
                labelText={showcase.uploadJikaLink}
                placeholder={showcase.captionPlaceholder}
                {...field}
              />
            );
          }}
        />
      </div>
    </SectionsLayout>
  );
}
