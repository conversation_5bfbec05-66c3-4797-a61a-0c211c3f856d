import {
  Control, Controller,
} from "react-hook-form";
import { ManageLinksFormDataType } from "@/app/cms/(protected)/showcase/[id]/types";
import { Input } from "@/common/components/molecules";
import lang from "@/common/lang";
import SectionsLayout from "@/app/cms/(protected)/showcase/[id]/SectionsLayout";
import SelectBox from "@/common/components/atoms/Selectbox";
import { useTickersList } from "@/app/cms/(protected)/showcase/[id]/hero-section/useTickersList";
import { CurrencyList } from "./constants";

type Props = { 
  control: Control<ManageLinksFormDataType>;
  privateShowcase: boolean;
  isGoogleFinanceEnabled: boolean;
};

const IndicatorsBar = ({
  control, privateShowcase, isGoogleFinanceEnabled,
}: Props) => {
  const { dashboard: { showcase } } = lang;
  const {
    tickersList,
  } = useTickersList();
  const exchanges = tickersList?.map((item) => {
    return {
      label: item.exchange,
      value: item.exchange_symbol,
    }
  });

  return (
    <SectionsLayout title={showcase.indicatorsBarSectionTitle}>
      <div className="flex flex-col gap-6">
        {
          privateShowcase ? (
            <>
              <div className="flex flex-col w-full gap-1">
                <label className="text-gray-900 text-sm font-medium">{showcase.raiseAmountLabel}</label>
                <div className="w-full flex gap-4">
                  <SelectBox
                    options={CurrencyList}
                    control={control}
                    placeholder={showcase.currencyPlaceholder}
                    fieldName="raiseAmountCurrency"
                  />
                  <Controller
                    control={control}
                    name="raiseAmount"
                    render={({
                      field, fieldState,
                    }) => {
                      const { error } = fieldState;
                      return (
                        <Input
                          width="w-full"
                          id={field.name}
                          error={!!error}
                          labelText=""
                          errorMessage={error?.message}
                          placeholder={showcase.raiseAmountPlaceholder}
                          data-cy="raiseAmount"
                          {...field}
                        />
                      );
                    }}
                  />
                </div>
              </div>
              <Controller
                control={control}
                name="equityOffered"
                render={({
                  field, fieldState,
                }) => {
                  const { error } = fieldState;
                  return (
                    <Input
                      width="w-full"
                      id={field.name}
                      error={!!error}
                      errorMessage={error?.message}
                      labelText={showcase.equityOfferedLabel}
                      placeholder={showcase.equityOfferedPlaceholder}
                      data-cy="equityOffered"
                      {...field}
                    />
                  );
                }}
              />
              <div className="flex flex-col w-full gap-1">
                <label className="text-gray-900 text-sm font-medium">{showcase.valuationLabel}</label>
                <div className="w-full flex gap-4">
                  <SelectBox
                    options={CurrencyList}
                    control={control}
                    placeholder={showcase.currencyPlaceholder}
                    fieldName="valuationCurrency"
                  />
                  <Controller
                    control={control}
                    name="valuation"
                    render={({
                      field, fieldState,
                    }) => {
                      const { error } = fieldState;
                      return (
                        <Input
                          width="w-full"
                          id={field.name}
                          error={!!error}
                          errorMessage={error?.message}
                          placeholder={showcase.valuationPlaceholder}
                          data-cy="valuation"
                          {...field}
                        />
                      );
                    }}
                  />
                </div>
              </div>
              <div className="flex flex-col w-full gap-1">
                <label className="text-gray-900 text-sm font-medium">{showcase.revenueLabel}</label>
                <div className="w-full flex gap-4">
                  <SelectBox
                    options={CurrencyList}
                    control={control}
                    placeholder={showcase.currencyPlaceholder}
                    fieldName="revenueCurrency"
                  />
                  <Controller
                    control={control}
                    name="revenue"
                    render={({
                      field, fieldState,
                    }) => {
                      const { error } = fieldState;
                      return (
                        <Input
                          width="w-full"
                          id={field.name}
                          error={!!error}
                          errorMessage={error?.message}
                          placeholder={showcase.revenuePlaceholder}
                          data-cy="revenue"
                          {...field}
                        />
                      );
                    }}
                  />
                </div>
              </div>
              <div className="flex flex-col w-full gap-1">
                <label className="text-gray-900 text-sm font-medium">{showcase.marketOpportunityLabel}</label>
                <div className="w-full flex gap-4">
                  <SelectBox
                    options={CurrencyList}
                    control={control}
                    placeholder={showcase.currencyPlaceholder}
                    fieldName="marketOpportunityCurrency"
                  />
                  <Controller
                    control={control}
                    name="marketOpportunity"
                    render={({
                      field, fieldState,
                    }) => {
                      const { error } = fieldState;
                      return (
                        <Input
                          width="w-full"
                          id={field.name}
                          error={!!error}
                          errorMessage={error?.message}
                          placeholder={showcase.marketOpportunityPlaceholder}
                          data-cy="marketOpportunity"
                          {...field}
                        />
                      );
                    }}
                  />
                </div>
              </div>
            </>
          ) : (
            <>
              {isGoogleFinanceEnabled && (
                <SelectBox
                  control={control}
                  fieldName="exchange"
                  label={showcase.stockExchangeLabel}
                  placeholder={showcase.stockExchangePlaceholder}
                  options={exchanges}
                  data-cy="exchange"
                />
              )}
              {isGoogleFinanceEnabled && (
                <Controller
                  control={control}
                  name="symbol"
                  render={({
                    field, fieldState,
                  }) => {
                    const { error } = fieldState;
                    return (
                      <Input
                        width="w-full"
                        id={field.name}
                        error={!!error}
                        errorMessage={error?.message}
                        labelText={showcase.stockTickerLabel}
                        placeholder={showcase.stockTickerPlaceholder}
                        data-cy="symbol"
                        {...field}
                      />
                    );
                  }}
                />
              )}
              <Controller
                control={control}
                name="targetPrice"
                render={({
                  field, fieldState,
                }) => {
                  const { error } = fieldState;
                  return (
                    <Input
                      width="w-full"
                      id={field.name}
                      error={!!error}
                      errorMessage={error?.message}
                      labelText={showcase.priceTargetLabel}
                      placeholder={showcase.priceTargetPlaceholder}
                      data-cy="targetPrice"
                      {...field}
                    />
                  );
                }}
              />
              <Controller
                control={control}
                name="priceTargetDisclaimer"
                render={({
                  field, fieldState,
                }) => {
                  const { error } = fieldState;
                  return (
                    <Input
                      width="w-full"
                      id={field.name}
                      error={!!error}
                      errorMessage={error?.message}
                      labelText={showcase.priceTargetDisclaimer}
                      placeholder={showcase.priceTargetDisclaimerPlaceholder}
                      data-cy="targetPriceDisclaimer"
                      {...field}
                    />
                  );
                }}
              />
            </>
          )
        }
      </div>
    </SectionsLayout>
  );
}

export default IndicatorsBar;
