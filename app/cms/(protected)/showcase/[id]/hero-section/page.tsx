'use client';
import FooterButtons from "../FooterButtons";
import { useGetShowcaseDetails } from "../hooks/useGetShowcaseDetails";
import { useFormData } from "./useFormData";
import { ShowcaseLayout } from "@/common/components/organisms/showcaseLayout";
import {
  Loader,
} from "@/common/components/molecules";
import {
  ShowcaseStatusEnum, ShowcaseStepNumber,
} from "@/app/cms/(protected)/showcase/[id]/types";
import lang from "@/common/lang";
import Navbar from "@/app/cms/(protected)/showcase/[id]/hero-section/Navbar";
import { HeroSection as TopHeroSection } from "@/app/cms/(protected)/showcase/[id]/hero-section/HeroSection";
import IndicatorsBar from "@/app/cms/(protected)/showcase/[id]/hero-section/IndicatorsBar";
import { VideoTab } from "./VideoTab";
import { SellCase } from "./SellCase";
import { BuyCase } from "./BuyCase";
import { StockChartsTab } from "./StockChartsTab";

const HeroSection = () => {
  const { showcaseDetails } = useGetShowcaseDetails();
  const {
    control, setValue, getValues, watch, onSubmit, setImageName, isLoading, setError, clearErrors,
  } = useFormData(showcaseDetails);
  const { dashboard } = lang;
  const isPrivate = { private: Boolean(showcaseDetails?.privateShowcase) };
  return (
    <ShowcaseLayout isPrivate={isPrivate.private} name={showcaseDetails?.title} step={ShowcaseStepNumber.heroSection}>
      {isLoading && <Loader />}
      <form onSubmit={onSubmit}>
        <div className="flex flex-col justify-center gap-6">
          <div className="flex flex-col gap-1">
            <h1 className="text-[#29282D] text-xl font-bold">{dashboard.showcase.heroSection}</h1>
            <p className="text-[#737373] text-base font-normal m-0">{dashboard.showcase.heroSectionSubtitle}</p>
          </div>
          <div className="flex flex-col justify-center items-center w-[840px] gap-6">
            <Navbar control={control} getValues={getValues}/>
            <TopHeroSection
              control={control}
              setImageName={setImageName}
              setValue={setValue}
              setError={setError}
              clearErrors={clearErrors}
              getValues={getValues}
            />
            <VideoTab
              control={control}
              setValue={setValue}
              setError={setError}
              clearErrors={clearErrors}
              getValues={getValues}
            />
            {!isPrivate.private && (
              <BuyCase
                control={control}
                watch={watch}
                setValue={setValue}
                setError={setError}
                clearErrors={clearErrors}
                getValues={getValues}
              />
            )}
            {!isPrivate.private && (
              <SellCase
                control={control}
                watch={watch}
                setValue={setValue}
                setError={setError}
                clearErrors={clearErrors}
                getValues={getValues}
              />
            )}
            <IndicatorsBar control={control} isGoogleFinanceEnabled={showcaseDetails?.isGoogleFinanceEnabled} privateShowcase={showcaseDetails.privateShowcase} />
            {!isPrivate.private && (
              <StockChartsTab 
                control={control}
                isGoogleFinanceEnabled={showcaseDetails?.isGoogleFinanceEnabled}
                setValue={setValue}
                setError={setError}
                clearErrors={clearErrors}
                getValues={getValues}
              />
            )}
            <FooterButtons live={showcaseDetails?.status === ShowcaseStatusEnum.live}/>
          </div>
        </div>
      </form>
    </ShowcaseLayout>
  );
};

export default HeroSection;
